// Portal Jumper - 2D Platformer Puzzle Game
// Core Game Engine

class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.width = this.canvas.width;
        this.height = this.canvas.height;
        
        // Game state
        this.gameState = 'start'; // 'start', 'playing', 'paused', 'gameOver', 'levelComplete'
        this.currentLevel = 1;
        this.score = 0;
        this.keysCollected = 0;
        this.totalKeys = 0;
        
        // Game objects
        this.player = null;
        this.platforms = [];
        this.keys = [];
        this.doors = [];
        this.switches = [];
        this.movingPlatforms = [];
        this.portal = null;
        
        // Input handling
        this.keys_pressed = {};
        this.setupInput();

        // Sound effects
        this.sounds = {
            jump: this.createSound(200, 0.1, 'square'),
            collect: this.createSound(400, 0.1, 'sine'),
            door: this.createSound(150, 0.2, 'sawtooth'),
            portal: this.createSound(300, 0.3, 'sine')
        };
        
        // Game loop
        this.lastTime = 0;
        this.gameLoop = this.gameLoop.bind(this);
        
        // Initialize
        this.init();
    }
    
    init() {
        this.hideAllScreens();
        document.getElementById('startScreen').classList.remove('hidden');
    }
    
    setupInput() {
        document.addEventListener('keydown', (e) => {
            this.keys_pressed[e.code] = true;
            e.preventDefault();
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys_pressed[e.code] = false;
            e.preventDefault();
        });
    }
    
    hideAllScreens() {
        document.getElementById('startScreen').classList.add('hidden');
        document.getElementById('gameOverScreen').classList.add('hidden');
    }
    
    updateUI() {
        document.getElementById('currentLevel').textContent = this.currentLevel;
        document.getElementById('score').textContent = this.score;
        document.getElementById('keysCollected').textContent = this.keysCollected;
        document.getElementById('totalKeys').textContent = this.totalKeys;
    }
    
    start() {
        this.gameState = 'playing';
        this.hideAllScreens();
        this.loadLevel(this.currentLevel);
        this.updateUI();
        requestAnimationFrame(this.gameLoop);
    }
    
    restart() {
        this.currentLevel = 1;
        this.score = 0;
        this.start();
    }
    
    gameLoop(currentTime) {
        if (this.gameState !== 'playing') return;
        
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Update
        this.update(deltaTime);
        
        // Render
        this.render();
        
        // Continue loop
        requestAnimationFrame(this.gameLoop);
    }
    
    update(deltaTime) {
        if (this.player) {
            this.player.update(deltaTime, this);
        }
        
        // Update moving platforms
        this.movingPlatforms.forEach(platform => {
            if (platform.update) platform.update(deltaTime);
        });

        // Update keys
        this.keys.forEach(key => {
            if (key.update) key.update(deltaTime);
        });

        // Update portal
        if (this.portal && this.portal.update) {
            this.portal.update(deltaTime);
        }
        
        // Check collisions
        this.checkCollisions();
        
        // Check win condition
        this.checkWinCondition();
    }
    
    render() {
        // Clear canvas
        this.ctx.fillStyle = '#87ceeb';
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        // Draw background elements
        this.drawBackground();
        
        // Draw platforms
        this.platforms.forEach(platform => platform.draw(this.ctx));
        this.movingPlatforms.forEach(platform => platform.draw(this.ctx));
        
        // Draw game objects
        this.keys.forEach(key => key.draw(this.ctx));
        this.doors.forEach(door => door.draw(this.ctx));
        this.switches.forEach(switch_ => switch_.draw(this.ctx));
        
        // Draw portal
        if (this.portal) this.portal.draw(this.ctx);
        
        // Draw player
        if (this.player) this.player.draw(this.ctx);
    }
    
    drawBackground() {
        // Draw simple cloud-like background
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        for (let i = 0; i < 5; i++) {
            const x = (i * 200 + 50) % this.width;
            const y = 50 + Math.sin(Date.now() * 0.001 + i) * 20;
            this.drawCloud(x, y);
        }
    }
    
    drawCloud(x, y) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, 20, 0, Math.PI * 2);
        this.ctx.arc(x + 25, y, 25, 0, Math.PI * 2);
        this.ctx.arc(x + 50, y, 20, 0, Math.PI * 2);
        this.ctx.arc(x + 25, y - 15, 15, 0, Math.PI * 2);
        this.ctx.fill();
    }
    
    checkCollisions() {
        if (!this.player) return;

        const playerBounds = this.player.getBounds();

        // Check platform collisions
        [...this.platforms, ...this.movingPlatforms].forEach(platform => {
            const platformBounds = platform.getBounds();

            if (this.isColliding(playerBounds, platformBounds)) {
                // Determine collision direction
                const overlapLeft = playerBounds.right - platformBounds.left;
                const overlapRight = platformBounds.right - playerBounds.left;
                const overlapTop = playerBounds.bottom - platformBounds.top;
                const overlapBottom = platformBounds.bottom - playerBounds.top;

                const minOverlap = Math.min(overlapLeft, overlapRight, overlapTop, overlapBottom);

                if (minOverlap === overlapTop && this.player.velocityY > 0) {
                    // Landing on top of platform
                    this.player.y = platformBounds.top - this.player.height;
                    this.player.velocityY = 0;
                    this.player.onGround = true;
                } else if (minOverlap === overlapBottom && this.player.velocityY < 0) {
                    // Hitting platform from below
                    this.player.y = platformBounds.bottom;
                    this.player.velocityY = 0;
                } else if (minOverlap === overlapLeft) {
                    // Hitting from the right
                    this.player.x = platformBounds.left - this.player.width;
                } else if (minOverlap === overlapRight) {
                    // Hitting from the left
                    this.player.x = platformBounds.right;
                }
            }
        });

        // Check key collection
        this.keys.forEach(key => {
            if (!key.collected && this.isColliding(playerBounds, key.getBounds())) {
                key.collected = true;
                this.keysCollected++;
                this.score += 50;
                this.updateUI();
                this.playSound('collect');

                // Check if doors should open
                this.doors.forEach(door => {
                    if (this.keysCollected >= door.keysRequired && !door.isOpen) {
                        door.isOpen = true;
                        this.playSound('door');
                    }
                });
            }
        });

        // Check door collisions (only if door is closed)
        this.doors.forEach(door => {
            const doorBounds = door.getBounds();
            if (doorBounds && this.isColliding(playerBounds, doorBounds)) {
                // Push player away from door
                const overlapLeft = playerBounds.right - doorBounds.left;
                const overlapRight = doorBounds.right - playerBounds.left;

                if (overlapLeft < overlapRight) {
                    this.player.x = doorBounds.left - this.player.width;
                } else {
                    this.player.x = doorBounds.right;
                }
            }
        });
    }

    isColliding(rect1, rect2) {
        return rect1.left < rect2.right &&
               rect1.right > rect2.left &&
               rect1.top < rect2.bottom &&
               rect1.bottom > rect2.top;
    }
    
    checkWinCondition() {
        // Check if player reached portal with all keys
        if (this.player && this.portal && this.keysCollected >= this.totalKeys) {
            const dx = this.player.x - this.portal.x;
            const dy = this.player.y - this.portal.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 30) {
                this.playSound('portal');
                this.levelComplete();
            }
        }
    }
    
    levelComplete() {
        this.score += 100 * this.currentLevel;
        this.currentLevel++;
        
        if (this.currentLevel > 5) { // Assuming 5 levels total
            this.gameWin();
        } else {
            setTimeout(() => {
                this.loadLevel(this.currentLevel);
                this.updateUI();
            }, 1000);
        }
    }
    
    gameWin() {
        this.gameState = 'gameOver';
        document.getElementById('gameOverTitle').textContent = 'Congratulations!';
        document.getElementById('gameOverMessage').textContent = `You completed all levels! Final Score: ${this.score}`;
        document.getElementById('gameOverScreen').classList.remove('hidden');
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        document.getElementById('gameOverTitle').textContent = 'Game Over!';
        document.getElementById('gameOverMessage').textContent = 'Try again!';
        document.getElementById('gameOverScreen').classList.remove('hidden');
    }
    
    loadLevel(levelNumber) {
        // Clear existing objects
        this.platforms = [];
        this.keys = [];
        this.doors = [];
        this.switches = [];
        this.movingPlatforms = [];
        this.portal = null;
        this.keysCollected = 0;

        // Get level data
        const levelData = this.getLevelData(levelNumber);

        // Create player
        this.player = new Player(levelData.playerStart.x, levelData.playerStart.y);

        // Create platforms
        levelData.platforms.forEach(p => {
            this.platforms.push(new Platform(p.x, p.y, p.width, p.height, p.color));
        });

        // Create moving platforms
        if (levelData.movingPlatforms) {
            levelData.movingPlatforms.forEach(mp => {
                this.movingPlatforms.push(new MovingPlatform(mp.x, mp.y, mp.width, mp.height, mp.path, mp.speed));
            });
        }

        // Create keys
        levelData.keys.forEach(k => {
            this.keys.push(new Key(k.x, k.y));
        });

        // Create doors
        if (levelData.doors) {
            levelData.doors.forEach(d => {
                this.doors.push(new Door(d.x, d.y, d.width, d.height, d.keysRequired));
            });
        }

        // Create switches
        if (levelData.switches) {
            levelData.switches.forEach(s => {
                this.switches.push(new Switch(s.x, s.y, s.targetId));
            });
        }

        // Create portal
        this.portal = new Portal(levelData.portal.x, levelData.portal.y);

        // Set total keys for UI
        this.totalKeys = this.keys.length;
    }

    getLevelData(levelNumber) {
        const levels = {
            1: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 200, height: 50 },
                    { x: 300, y: 450, width: 150, height: 20 },
                    { x: 550, y: 350, width: 150, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 }, // Top boundary
                    { x: 0, y: 0, width: 20, height: 600 }, // Left boundary
                    { x: 780, y: 0, width: 20, height: 600 }, // Right boundary
                    { x: 0, y: 580, width: 800, height: 20 } // Bottom boundary
                ],
                keys: [
                    { x: 375, y: 420 }
                ],
                portal: { x: 600, y: 300 }
            },
            2: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 150, height: 50 },
                    { x: 200, y: 480, width: 100, height: 20 },
                    { x: 350, y: 400, width: 100, height: 20 },
                    { x: 500, y: 320, width: 100, height: 20 },
                    { x: 650, y: 250, width: 150, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                keys: [
                    { x: 225, y: 450 },
                    { x: 525, y: 290 }
                ],
                doors: [
                    { x: 600, y: 200, width: 20, height: 50, keysRequired: 2 }
                ],
                portal: { x: 700, y: 200 }
            },
            3: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 120, height: 50 },
                    { x: 180, y: 480, width: 80, height: 20 },
                    { x: 320, y: 420, width: 80, height: 20 },
                    { x: 460, y: 360, width: 80, height: 20 },
                    { x: 600, y: 300, width: 80, height: 20 },
                    { x: 300, y: 200, width: 200, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                keys: [
                    { x: 205, y: 450 },
                    { x: 345, y: 390 },
                    { x: 625, y: 270 }
                ],
                doors: [
                    { x: 280, y: 150, width: 20, height: 50, keysRequired: 3 }
                ],
                portal: { x: 400, y: 150 }
            },
            4: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 100, height: 50 },
                    { x: 150, y: 500, width: 60, height: 20 },
                    { x: 250, y: 450, width: 60, height: 20 },
                    { x: 350, y: 400, width: 60, height: 20 },
                    { x: 450, y: 350, width: 60, height: 20 },
                    { x: 550, y: 300, width: 60, height: 20 },
                    { x: 650, y: 250, width: 60, height: 20 },
                    { x: 200, y: 150, width: 400, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                keys: [
                    { x: 175, y: 470 },
                    { x: 375, y: 370 },
                    { x: 575, y: 270 },
                    { x: 675, y: 220 }
                ],
                doors: [
                    { x: 180, y: 100, width: 20, height: 50, keysRequired: 2 },
                    { x: 580, y: 100, width: 20, height: 50, keysRequired: 4 }
                ],
                portal: { x: 400, y: 100 }
            },
            5: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 80, height: 50 },
                    { x: 120, y: 480, width: 40, height: 20 },
                    { x: 200, y: 420, width: 40, height: 20 },
                    { x: 280, y: 360, width: 40, height: 20 },
                    { x: 360, y: 300, width: 40, height: 20 },
                    { x: 440, y: 240, width: 40, height: 20 },
                    { x: 520, y: 180, width: 40, height: 20 },
                    { x: 600, y: 120, width: 40, height: 20 },
                    { x: 680, y: 60, width: 120, height: 20 },
                    { x: 100, y: 200, width: 150, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                keys: [
                    { x: 145, y: 450 },
                    { x: 305, y: 330 },
                    { x: 465, y: 210 },
                    { x: 625, y: 90 },
                    { x: 175, y: 170 }
                ],
                doors: [
                    { x: 80, y: 150, width: 20, height: 50, keysRequired: 3 },
                    { x: 660, y: 10, width: 20, height: 50, keysRequired: 5 }
                ],
                portal: { x: 720, y: 10 }
            }
        };

        // Add more levels with similar structure
        if (!levels[levelNumber]) {
            // Generate a simple level if not defined
            return {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 800, height: 50 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 }
                ],
                keys: [{ x: 400, y: 520 }],
                portal: { x: 700, y: 500 }
            };
        }

        return levels[levelNumber];
    }

    createSound(frequency, duration, type = 'sine') {
        return () => {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = frequency;
                oscillator.type = type;

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            } catch (e) {
                // Ignore audio errors
            }
        };
    }

    playSound(soundName) {
        if (this.sounds[soundName]) {
            this.sounds[soundName]();
        }
    }
}

// Global game instance
let game;

// Player class
class Player {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 32;
        this.velocityX = 0;
        this.velocityY = 0;
        this.speed = 200;
        this.jumpPower = 400;
        this.gravity = 800;
        this.onGround = false;
        this.color = '#ff4444';
    }

    update(deltaTime, game) {
        const dt = deltaTime / 1000; // Convert to seconds

        // Handle input
        this.handleInput(game.keys_pressed, dt);

        // Apply gravity
        if (!this.onGround) {
            this.velocityY += this.gravity * dt;
        }

        // Update position
        this.x += this.velocityX * dt;
        this.y += this.velocityY * dt;

        // Keep player in bounds
        if (this.x < 0) this.x = 0;
        if (this.x + this.width > game.width) this.x = game.width - this.width;

        // Check if player fell off the screen
        if (this.y > game.height + 100) {
            game.gameOver();
        }

        // Reset onGround flag (will be set by collision detection)
        this.onGround = false;
    }

    handleInput(keys, dt) {
        // Horizontal movement
        this.velocityX = 0;

        if (keys['ArrowLeft'] || keys['KeyA']) {
            this.velocityX = -this.speed;
        }
        if (keys['ArrowRight'] || keys['KeyD']) {
            this.velocityX = this.speed;
        }

        // Jumping
        if ((keys['ArrowUp'] || keys['KeyW'] || keys['Space']) && this.onGround) {
            this.velocityY = -this.jumpPower;
            this.onGround = false;
            // Play jump sound through game instance
            if (window.game) window.game.playSound('jump');
        }
    }

    draw(ctx) {
        // Draw player as a simple character
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Draw simple face
        ctx.fillStyle = 'white';
        ctx.fillRect(this.x + 6, this.y + 8, 4, 4); // Left eye
        ctx.fillRect(this.x + 14, this.y + 8, 4, 4); // Right eye

        ctx.fillStyle = 'black';
        ctx.fillRect(this.x + 7, this.y + 9, 2, 2); // Left pupil
        ctx.fillRect(this.x + 15, this.y + 9, 2, 2); // Right pupil

        // Simple smile
        ctx.fillStyle = 'white';
        ctx.fillRect(this.x + 8, this.y + 18, 8, 2);
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// Platform class
class Platform {
    constructor(x, y, width, height, color = '#4a4a4a') {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.color = color;
    }

    draw(ctx) {
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Add some texture
        ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.fillRect(this.x, this.y, this.width, 2);
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// Key class
class Key {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        this.collected = false;
        this.rotation = 0;
    }

    update(deltaTime) {
        this.rotation += deltaTime * 0.003;
    }

    draw(ctx) {
        if (this.collected) return;

        ctx.save();
        ctx.translate(this.x + this.width/2, this.y + this.height/2);
        ctx.rotate(this.rotation);

        // Draw key
        ctx.fillStyle = '#ffd700';
        ctx.fillRect(-8, -8, 16, 16);

        // Key details
        ctx.fillStyle = '#ffed4e';
        ctx.fillRect(-6, -6, 12, 12);
        ctx.fillStyle = '#ffd700';
        ctx.fillRect(-2, -2, 4, 4);

        ctx.restore();
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// Door class
class Door {
    constructor(x, y, width, height, keysRequired = 1) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.keysRequired = keysRequired;
        this.isOpen = false;
    }

    draw(ctx) {
        if (this.isOpen) return;

        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Door handle
        ctx.fillStyle = '#FFD700';
        ctx.fillRect(this.x + this.width - 8, this.y + this.height/2 - 2, 4, 4);

        // Door frame
        ctx.strokeStyle = '#654321';
        ctx.lineWidth = 2;
        ctx.strokeRect(this.x, this.y, this.width, this.height);
    }

    getBounds() {
        return this.isOpen ? null : {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// Portal class
class Portal {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 60;
        this.animation = 0;
    }

    update(deltaTime) {
        this.animation += deltaTime * 0.005;
    }

    draw(ctx) {
        ctx.save();

        // Portal effect
        const centerX = this.x + this.width/2;
        const centerY = this.y + this.height/2;

        for (let i = 0; i < 3; i++) {
            const radius = 15 + i * 8 + Math.sin(this.animation + i) * 3;
            const alpha = 0.3 - i * 0.1;

            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(138, 43, 226, ${alpha})`;
            ctx.fill();
        }

        // Inner portal
        ctx.beginPath();
        ctx.arc(centerX, centerY, 8, 0, Math.PI * 2);
        ctx.fillStyle = '#9370DB';
        ctx.fill();

        ctx.restore();
    }
}

// Global functions for HTML buttons
function startGame() {
    if (!game) {
        game = new Game();
    }
    game.start();
}

function restartGame() {
    game.restart();
}

function showStartScreen() {
    game.gameState = 'start';
    game.hideAllScreens();
    document.getElementById('startScreen').classList.remove('hidden');
}

// Initialize when page loads
window.addEventListener('load', () => {
    game = new Game();
});
