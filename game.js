// Portal Jumper - 2D Platformer Puzzle Game
// Core Game Engine

class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.width = this.canvas.width;
        this.height = this.canvas.height;
        
        // Game state
        this.gameState = 'start'; // 'start', 'playing', 'paused', 'gameOver', 'levelComplete'
        this.currentLevel = 1;
        this.score = 0;
        this.keysCollected = 0;
        this.totalKeys = 0;
        
        // Game objects
        this.player = null;
        this.platforms = [];
        this.keys = [];
        this.doors = [];
        this.switches = [];
        this.movingPlatforms = [];
        this.pressurePlates = [];
        this.powerUps = [];
        this.enemies = [];
        this.checkpoints = [];
        this.particles = [];
        this.portal = null;

        // Game state
        this.lives = 3;
        this.maxLives = 3;
        this.currentCheckpoint = null;
        this.isPaused = false;
        this.camera = { x: 0, y: 0 };
        this.screenShake = { x: 0, y: 0, intensity: 0, duration: 0 };

        // Tutorial system
        this.tutorialShown = {
            powerUp: false,
            movingPlatform: false,
            enemy: false,
            pressurePlate: false
        };
        this.currentTutorial = null;
        this.tutorialTimer = 0;
        
        // Input handling
        this.keys_pressed = {};
        this.setupInput();

        // Sound effects
        this.sounds = {
            jump: this.createSound(200, 0.1, 'square'),
            collect: this.createSound(400, 0.1, 'sine'),
            door: this.createSound(150, 0.2, 'sawtooth'),
            portal: this.createSound(300, 0.3, 'sine')
        };

        // Background music
        this.musicPlaying = false;
        this.musicContext = null;

        // Local storage
        this.highScore = parseInt(localStorage.getItem('portalJumperHighScore') || '0');
        this.unlockedLevels = parseInt(localStorage.getItem('portalJumperUnlockedLevels') || '1');
        
        // Game loop
        this.lastTime = 0;
        this.gameLoop = this.gameLoop.bind(this);
        
        // Initialize
        this.init();

        // Responsive canvas
        this.setupResponsiveCanvas();
    }
    
    init() {
        this.hideAllScreens();
        document.getElementById('startScreen').classList.remove('hidden');
    }
    
    setupInput() {
        document.addEventListener('keydown', (e) => {
            this.keys_pressed[e.code] = true;
            e.preventDefault();
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys_pressed[e.code] = false;
            e.preventDefault();
        });

        // Pause functionality
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Escape' && this.gameState === 'playing') {
                this.togglePause();
                e.preventDefault();
            }
        });
    }
    
    hideAllScreens() {
        document.getElementById('startScreen').classList.add('hidden');
        document.getElementById('gameOverScreen').classList.add('hidden');
        const levelSelect = document.getElementById('levelSelectScreen');
        if (levelSelect) levelSelect.classList.add('hidden');
    }
    
    updateUI() {
        document.getElementById('currentLevel').textContent = this.currentLevel;
        document.getElementById('score').textContent = this.score;
        document.getElementById('highScore').textContent = this.highScore;
        document.getElementById('keysCollected').textContent = this.keysCollected;
        document.getElementById('totalKeys').textContent = this.totalKeys;
        document.getElementById('lives').textContent = this.lives;
    }
    
    start() {
        this.gameState = 'playing';
        this.hideAllScreens();
        this.loadLevel(this.currentLevel);
        this.updateUI();
        this.startBackgroundMusic();
        requestAnimationFrame(this.gameLoop);
    }
    
    restart() {
        this.currentLevel = 1;
        this.score = 0;
        this.start();
    }
    
    gameLoop(currentTime) {
        if (this.gameState !== 'playing') return;

        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;

        // Update (only if not paused)
        if (!this.isPaused) {
            this.update(deltaTime);
        }

        // Render
        this.render();

        // Continue loop
        requestAnimationFrame(this.gameLoop);
    }
    
    update(deltaTime) {
        if (this.player) {
            this.player.update(deltaTime, this);
        }
        
        // Update moving platforms
        this.movingPlatforms.forEach(platform => {
            if (platform.update) platform.update(deltaTime);
        });

        // Update keys
        this.keys.forEach(key => {
            if (key.update) key.update(deltaTime);
        });

        // Update power-ups
        this.powerUps.forEach(powerUp => {
            if (powerUp.update) powerUp.update(deltaTime);
        });

        // Update enemies
        this.enemies.forEach(enemy => {
            if (enemy.update) enemy.update(deltaTime, this);
        });

        // Update pressure plates
        this.pressurePlates.forEach(plate => {
            if (plate.update && this.player) plate.update(this.player);
        });

        // Update portal
        if (this.portal && this.portal.update) {
            this.portal.update(deltaTime);
        }

        // Update screen shake
        if (this.screenShake.duration > 0) {
            this.screenShake.duration -= deltaTime;
            this.screenShake.x = (Math.random() - 0.5) * this.screenShake.intensity;
            this.screenShake.y = (Math.random() - 0.5) * this.screenShake.intensity;

            if (this.screenShake.duration <= 0) {
                this.screenShake.x = 0;
                this.screenShake.y = 0;
                this.screenShake.intensity = 0;
            }
        }

        // Update particles
        this.updateParticles(deltaTime);

        // Update tutorial
        this.updateTutorial(deltaTime);
        
        // Check collisions
        this.checkCollisions();
        
        // Check win condition
        this.checkWinCondition();
    }
    
    render() {
        // Update camera
        this.updateCamera();

        // Apply camera transform
        this.ctx.save();
        this.ctx.translate(-this.camera.x + this.screenShake.x, -this.camera.y + this.screenShake.y);

        // Clear canvas with larger area for camera movement
        this.ctx.fillStyle = '#87ceeb';
        this.ctx.fillRect(this.camera.x - 100, this.camera.y - 100, this.width + 200, this.height + 200);

        // Draw parallax background
        this.drawParallaxBackground();

        // Draw background elements
        this.drawBackground();
        
        // Draw platforms
        this.platforms.forEach(platform => platform.draw(this.ctx));
        this.movingPlatforms.forEach(platform => platform.draw(this.ctx));
        
        // Draw game objects
        this.keys.forEach(key => key.draw(this.ctx));
        this.doors.forEach(door => door.draw(this.ctx));
        this.switches.forEach(switch_ => switch_.draw(this.ctx));
        this.pressurePlates.forEach(plate => plate.draw(this.ctx));
        this.powerUps.forEach(powerUp => powerUp.draw(this.ctx));
        this.enemies.forEach(enemy => enemy.draw(this.ctx));

        // Draw portal
        if (this.portal) this.portal.draw(this.ctx);

        // Draw player
        if (this.player) this.player.draw(this.ctx);

        // Draw particles
        this.drawParticles();

        // Draw UI overlays
        this.drawHealthBar();
        this.drawPowerUpIndicators();

        // Draw pause overlay
        if (this.isPaused) {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            this.ctx.fillRect(0, 0, this.width, this.height);

            this.ctx.fillStyle = 'white';
            this.ctx.font = '48px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('PAUSED', this.width/2, this.height/2);
            this.ctx.textAlign = 'left';
        }

        // Draw tutorial overlay
        this.drawTutorial();

        // Restore camera transform
        this.ctx.restore();
    }
    
    drawBackground() {
        // Draw simple cloud-like background
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        for (let i = 0; i < 5; i++) {
            const x = (i * 200 + 50) % this.width;
            const y = 50 + Math.sin(Date.now() * 0.001 + i) * 20;
            this.drawCloud(x, y);
        }
    }
    
    drawCloud(x, y) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, 20, 0, Math.PI * 2);
        this.ctx.arc(x + 25, y, 25, 0, Math.PI * 2);
        this.ctx.arc(x + 50, y, 20, 0, Math.PI * 2);
        this.ctx.arc(x + 25, y - 15, 15, 0, Math.PI * 2);
        this.ctx.fill();
    }

    updateCamera() {
        if (!this.player) return;

        // Smooth camera following
        const targetX = this.player.x - this.width / 2;
        const targetY = this.player.y - this.height / 2;

        this.camera.x += (targetX - this.camera.x) * 0.1;
        this.camera.y += (targetY - this.camera.y) * 0.1;

        // Keep camera within level bounds (if needed)
        this.camera.x = Math.max(0, Math.min(this.camera.x, 1600 - this.width));
        this.camera.y = Math.max(0, Math.min(this.camera.y, 1200 - this.height));
    }

    drawParallaxBackground() {
        // Draw distant mountains
        this.ctx.fillStyle = 'rgba(100, 100, 150, 0.3)';
        for (let i = 0; i < 5; i++) {
            const x = (i * 300 - this.camera.x * 0.2) % (this.width + 300);
            const y = this.height * 0.7 - this.camera.y * 0.1;
            this.drawMountain(x, y, 200, 150);
        }

        // Draw mid-ground hills
        this.ctx.fillStyle = 'rgba(120, 150, 100, 0.4)';
        for (let i = 0; i < 8; i++) {
            const x = (i * 200 - this.camera.x * 0.5) % (this.width + 200);
            const y = this.height * 0.8 - this.camera.y * 0.3;
            this.drawHill(x, y, 150, 80);
        }
    }

    drawMountain(x, y, width, height) {
        this.ctx.beginPath();
        this.ctx.moveTo(x, y);
        this.ctx.lineTo(x + width/2, y - height);
        this.ctx.lineTo(x + width, y);
        this.ctx.closePath();
        this.ctx.fill();
    }

    drawHill(x, y, width, height) {
        this.ctx.beginPath();
        this.ctx.ellipse(x + width/2, y, width/2, height/2, 0, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawHealthBar() {
        if (!this.player) return;

        const barWidth = 100;
        const barHeight = 10;
        const x = 20;
        const y = 20;

        // Background
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.fillRect(x - 2, y - 2, barWidth + 4, barHeight + 4);

        // Health bar background
        this.ctx.fillStyle = '#444';
        this.ctx.fillRect(x, y, barWidth, barHeight);

        // Health bar fill
        const healthPercent = this.player.health / this.player.maxHealth;
        const fillWidth = barWidth * healthPercent;

        if (healthPercent > 0.6) {
            this.ctx.fillStyle = '#00FF00';
        } else if (healthPercent > 0.3) {
            this.ctx.fillStyle = '#FFFF00';
        } else {
            this.ctx.fillStyle = '#FF0000';
        }

        this.ctx.fillRect(x, y, fillWidth, barHeight);

        // Lives indicator
        this.ctx.fillStyle = '#FFD700';
        this.ctx.font = '14px Arial';
        this.ctx.fillText(`Lives: ${this.lives}`, x, y + 25);
    }

    drawPowerUpIndicators() {
        if (!this.player) return;

        const x = this.width - 150;
        let y = 20;

        // Double jump indicator
        if (this.player.hasDoubleJump) {
            this.ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
            this.ctx.fillRect(x, y, 30, 30);
            this.ctx.fillStyle = 'white';
            this.ctx.font = '12px Arial';
            this.ctx.fillText('DJ', x + 8, y + 20);
            y += 35;
        }

        // Speed boost indicator
        if (this.player.speedBoostTime > 0) {
            this.ctx.fillStyle = 'rgba(255, 69, 0, 0.8)';
            this.ctx.fillRect(x, y, 30, 30);
            this.ctx.fillStyle = 'white';
            this.ctx.font = '12px Arial';
            this.ctx.fillText('SP', x + 8, y + 20);
            y += 35;
        }

        // Invincibility indicator
        if (this.player.invincibilityTime > 0) {
            this.ctx.fillStyle = 'rgba(147, 112, 219, 0.8)';
            this.ctx.fillRect(x, y, 30, 30);
            this.ctx.fillStyle = 'white';
            this.ctx.font = '12px Arial';
            this.ctx.fillText('INV', x + 6, y + 20);
        }
    }
    
    checkCollisions() {
        if (!this.player) return;

        const playerBounds = this.player.getBounds();

        // Check platform collisions
        [...this.platforms, ...this.movingPlatforms].forEach(platform => {
            const platformBounds = platform.getBounds();

            if (this.isColliding(playerBounds, platformBounds)) {
                // Determine collision direction
                const overlapLeft = playerBounds.right - platformBounds.left;
                const overlapRight = platformBounds.right - playerBounds.left;
                const overlapTop = playerBounds.bottom - platformBounds.top;
                const overlapBottom = platformBounds.bottom - playerBounds.top;

                const minOverlap = Math.min(overlapLeft, overlapRight, overlapTop, overlapBottom);

                if (minOverlap === overlapTop && this.player.velocityY > 0) {
                    // Landing on top of platform
                    this.player.y = platformBounds.top - this.player.height;
                    this.player.velocityY = 0;
                    this.player.onGround = true;
                } else if (minOverlap === overlapBottom && this.player.velocityY < 0) {
                    // Hitting platform from below
                    this.player.y = platformBounds.bottom;
                    this.player.velocityY = 0;
                } else if (minOverlap === overlapLeft) {
                    // Hitting from the right
                    this.player.x = platformBounds.left - this.player.width;
                } else if (minOverlap === overlapRight) {
                    // Hitting from the left
                    this.player.x = platformBounds.right;
                }
            }
        });

        // Check key collection
        this.keys.forEach(key => {
            if (!key.collected && this.isColliding(playerBounds, key.getBounds())) {
                key.collected = true;
                this.keysCollected++;
                this.score += 50;
                this.updateUI();
                this.playSound('collect');

                // Create particle effect
                for (let i = 0; i < 8; i++) {
                    this.createParticle(key.x + key.width/2, key.y + key.height/2, '#FFD700');
                }

                // Check if doors should open
                this.doors.forEach(door => {
                    if (this.keysCollected >= door.keysRequired && !door.isOpen) {
                        door.isOpen = true;
                        this.playSound('door');
                    }
                });
            }
        });

        // Check door collisions (only if door is closed)
        this.doors.forEach(door => {
            const doorBounds = door.getBounds();
            if (doorBounds && this.isColliding(playerBounds, doorBounds)) {
                // Push player away from door
                const overlapLeft = playerBounds.right - doorBounds.left;
                const overlapRight = doorBounds.right - playerBounds.left;

                if (overlapLeft < overlapRight) {
                    this.player.x = doorBounds.left - this.player.width;
                } else {
                    this.player.x = doorBounds.right;
                }
            }
        });

        // Check power-up collection
        this.powerUps.forEach(powerUp => {
            if (!powerUp.collected && this.isColliding(playerBounds, powerUp.getBounds())) {
                powerUp.collected = true;
                this.player.applyPowerUp(powerUp.type);
                this.score += 100;
                this.updateUI();
                this.playSound('collect');

                // Create power-up particle effect
                for (let i = 0; i < 12; i++) {
                    const colors = ['#00FF00', '#FF4500', '#9370DB'];
                    const color = colors[Math.floor(Math.random() * colors.length)];
                    this.createParticle(powerUp.x + powerUp.width/2, powerUp.y + powerUp.height/2, color);
                }
            }
        });

        // Check enemy collisions
        this.enemies.forEach(enemy => {
            if (this.isColliding(playerBounds, enemy.getBounds())) {
                if (!this.player.isInvulnerable()) {
                    this.player.takeDamage(this);
                }
            }

            // Enemy platform collisions
            [...this.platforms, ...this.movingPlatforms].forEach(platform => {
                const enemyBounds = enemy.getBounds();
                const platformBounds = platform.getBounds();

                if (this.isColliding(enemyBounds, platformBounds)) {
                    const overlapTop = enemyBounds.bottom - platformBounds.top;
                    const overlapBottom = platformBounds.bottom - enemyBounds.top;

                    if (overlapTop < overlapBottom && enemy.velocityY > 0) {
                        enemy.y = platformBounds.top - enemy.height;
                        enemy.velocityY = 0;
                        enemy.onGround = true;
                    }
                }
            });
        });
    }

    isColliding(rect1, rect2) {
        return rect1.left < rect2.right &&
               rect1.right > rect2.left &&
               rect1.top < rect2.bottom &&
               rect1.bottom > rect2.top;
    }
    
    checkWinCondition() {
        // Check if player reached portal with all keys
        if (this.player && this.portal && this.keysCollected >= this.totalKeys) {
            const dx = this.player.x - this.portal.x;
            const dy = this.player.y - this.portal.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 30) {
                this.playSound('portal');
                this.levelComplete();
            }
        }
    }
    
    levelComplete() {
        this.score += 100 * this.currentLevel;

        // Update high score
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('portalJumperHighScore', this.highScore.toString());
        }

        // Unlock next level
        if (this.currentLevel >= this.unlockedLevels) {
            this.unlockedLevels = this.currentLevel + 1;
            localStorage.setItem('portalJumperUnlockedLevels', this.unlockedLevels.toString());
        }

        this.currentLevel++;

        if (this.currentLevel > 8) { // Now we have 8 levels total
            this.gameWin();
        } else {
            setTimeout(() => {
                this.loadLevel(this.currentLevel);
                this.updateUI();
            }, 1000);
        }
    }
    
    gameWin() {
        this.gameState = 'gameOver';
        document.getElementById('gameOverTitle').textContent = 'Congratulations!';
        document.getElementById('gameOverMessage').textContent = `You completed all levels! Final Score: ${this.score}`;
        document.getElementById('gameOverScreen').classList.remove('hidden');
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        document.getElementById('gameOverTitle').textContent = 'Game Over!';
        document.getElementById('gameOverMessage').textContent = 'Try again!';
        document.getElementById('gameOverScreen').classList.remove('hidden');
    }

    loseLife() {
        this.lives--;

        if (this.lives <= 0) {
            this.gameOver();
        } else {
            // Respawn at checkpoint or level start
            if (this.currentCheckpoint) {
                this.player.x = this.currentCheckpoint.x;
                this.player.y = this.currentCheckpoint.y;
            } else {
                const levelData = this.getLevelData(this.currentLevel);
                this.player.x = levelData.playerStart.x;
                this.player.y = levelData.playerStart.y;
            }

            // Reset player health
            this.player.health = this.player.maxHealth;
            this.player.velocityX = 0;
            this.player.velocityY = 0;

            this.updateUI();
        }
    }
    
    loadLevel(levelNumber) {
        // Clear existing objects
        this.platforms = [];
        this.keys = [];
        this.doors = [];
        this.switches = [];
        this.movingPlatforms = [];
        this.pressurePlates = [];
        this.powerUps = [];
        this.enemies = [];
        this.checkpoints = [];
        this.portal = null;
        this.keysCollected = 0;
        this.currentCheckpoint = null;

        // Get level data
        const levelData = this.getLevelData(levelNumber);

        // Create player
        this.player = new Player(levelData.playerStart.x, levelData.playerStart.y);

        // Create platforms
        levelData.platforms.forEach(p => {
            this.platforms.push(new Platform(p.x, p.y, p.width, p.height, p.color));
        });

        // Create moving platforms
        if (levelData.movingPlatforms) {
            levelData.movingPlatforms.forEach(mp => {
                this.movingPlatforms.push(new MovingPlatform(mp.x, mp.y, mp.width, mp.height, mp.path, mp.speed));
            });
        }

        // Create keys
        levelData.keys.forEach(k => {
            this.keys.push(new Key(k.x, k.y));
        });

        // Create doors
        if (levelData.doors) {
            levelData.doors.forEach(d => {
                this.doors.push(new Door(d.x, d.y, d.width, d.height, d.keysRequired));
            });
        }

        // Create switches
        if (levelData.switches) {
            levelData.switches.forEach(s => {
                this.switches.push(new Switch(s.x, s.y, s.targetId));
            });
        }

        // Create pressure plates
        if (levelData.pressurePlates) {
            levelData.pressurePlates.forEach(pp => {
                this.pressurePlates.push(new PressurePlate(pp.x, pp.y, pp.targetIds, pp.stayPressed));
            });
        }

        // Create power-ups
        if (levelData.powerUps) {
            levelData.powerUps.forEach(pu => {
                this.powerUps.push(new PowerUp(pu.x, pu.y, pu.type));
            });
        }

        // Create enemies
        if (levelData.enemies) {
            levelData.enemies.forEach(e => {
                this.enemies.push(new Enemy(e.x, e.y, e.patrolStart, e.patrolEnd, e.speed));
            });
        }

        // Create portal
        this.portal = new Portal(levelData.portal.x, levelData.portal.y);

        // Set total keys for UI
        this.totalKeys = this.keys.length;

        // Reset player state
        this.player.health = this.player.maxHealth;
        this.lives = this.maxLives;

        // Check for tutorials
        setTimeout(() => this.checkTutorials(), 1000);
    }

    getLevelData(levelNumber) {
        const levels = {
            1: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 200, height: 50 },
                    { x: 300, y: 450, width: 150, height: 20 },
                    { x: 550, y: 350, width: 150, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 }, // Top boundary
                    { x: 0, y: 0, width: 20, height: 600 }, // Left boundary
                    { x: 780, y: 0, width: 20, height: 600 }, // Right boundary
                    { x: 0, y: 580, width: 800, height: 20 } // Bottom boundary
                ],
                keys: [
                    { x: 375, y: 420 }
                ],
                powerUps: [
                    { x: 125, y: 520, type: 'doubleJump' }
                ],
                portal: { x: 600, y: 300 }
            },
            2: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 150, height: 50 },
                    { x: 200, y: 480, width: 100, height: 20 },
                    { x: 350, y: 400, width: 100, height: 20 },
                    { x: 500, y: 320, width: 100, height: 20 },
                    { x: 650, y: 250, width: 150, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                keys: [
                    { x: 225, y: 450 },
                    { x: 525, y: 290 }
                ],
                movingPlatforms: [
                    { x: 300, y: 350, width: 80, height: 20, waypoints: [{x: 300, y: 350}, {x: 450, y: 350}], speed: 60 }
                ],
                doors: [
                    { x: 600, y: 200, width: 20, height: 50, keysRequired: 2 }
                ],
                powerUps: [
                    { x: 375, y: 320, type: 'speedBoost' }
                ],
                enemies: [
                    { x: 250, y: 450, patrolStart: 200, patrolEnd: 400, speed: 40 }
                ],
                portal: { x: 700, y: 200 }
            },
            3: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 120, height: 50 },
                    { x: 180, y: 480, width: 80, height: 20 },
                    { x: 320, y: 420, width: 80, height: 20 },
                    { x: 460, y: 360, width: 80, height: 20 },
                    { x: 600, y: 300, width: 80, height: 20 },
                    { x: 300, y: 200, width: 200, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                keys: [
                    { x: 205, y: 450 },
                    { x: 345, y: 390 },
                    { x: 625, y: 270 }
                ],
                doors: [
                    { x: 280, y: 150, width: 20, height: 50, keysRequired: 3 }
                ],
                portal: { x: 400, y: 150 }
            },
            4: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 100, height: 50 },
                    { x: 150, y: 500, width: 60, height: 20 },
                    { x: 250, y: 450, width: 60, height: 20 },
                    { x: 350, y: 400, width: 60, height: 20 },
                    { x: 450, y: 350, width: 60, height: 20 },
                    { x: 550, y: 300, width: 60, height: 20 },
                    { x: 650, y: 250, width: 60, height: 20 },
                    { x: 200, y: 150, width: 400, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                keys: [
                    { x: 175, y: 470 },
                    { x: 375, y: 370 },
                    { x: 575, y: 270 },
                    { x: 675, y: 220 }
                ],
                doors: [
                    { x: 180, y: 100, width: 20, height: 50, keysRequired: 2 },
                    { x: 580, y: 100, width: 20, height: 50, keysRequired: 4 }
                ],
                portal: { x: 400, y: 100 }
            },
            5: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 80, height: 50 },
                    { x: 120, y: 480, width: 40, height: 20 },
                    { x: 200, y: 420, width: 40, height: 20 },
                    { x: 280, y: 360, width: 40, height: 20 },
                    { x: 360, y: 300, width: 40, height: 20 },
                    { x: 440, y: 240, width: 40, height: 20 },
                    { x: 520, y: 180, width: 40, height: 20 },
                    { x: 600, y: 120, width: 40, height: 20 },
                    { x: 680, y: 60, width: 120, height: 20 },
                    { x: 100, y: 200, width: 150, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                keys: [
                    { x: 145, y: 450 },
                    { x: 305, y: 330 },
                    { x: 465, y: 210 },
                    { x: 625, y: 90 },
                    { x: 175, y: 170 }
                ],
                doors: [
                    { x: 80, y: 150, width: 20, height: 50, keysRequired: 3 },
                    { x: 660, y: 10, width: 20, height: 50, keysRequired: 5 }
                ],
                portal: { x: 720, y: 10 }
            },
            6: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 100, height: 50 },
                    { x: 200, y: 450, width: 80, height: 20 },
                    { x: 400, y: 350, width: 80, height: 20 },
                    { x: 600, y: 250, width: 80, height: 20 },
                    { x: 300, y: 150, width: 200, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                movingPlatforms: [
                    { x: 150, y: 400, width: 60, height: 15, waypoints: [{x: 150, y: 400}, {x: 150, y: 300}], speed: 40 },
                    { x: 500, y: 200, width: 60, height: 15, waypoints: [{x: 500, y: 200}, {x: 650, y: 200}], speed: 50 }
                ],
                keys: [
                    { x: 225, y: 420 },
                    { x: 425, y: 320 },
                    { x: 625, y: 220 }
                ],
                powerUps: [
                    { x: 75, y: 520, type: 'doubleJump' },
                    { x: 525, y: 170, type: 'speedBoost' }
                ],
                enemies: [
                    { x: 300, y: 420, patrolStart: 200, patrolEnd: 480, speed: 35 }
                ],
                pressurePlates: [
                    { x: 360, y: 142, targetIds: ['door1'], stayPressed: true }
                ],
                doors: [
                    { x: 280, y: 100, width: 20, height: 50, keysRequired: 3 }
                ],
                portal: { x: 400, y: 100 }
            },
            7: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 80, height: 50 },
                    { x: 120, y: 480, width: 60, height: 20 },
                    { x: 220, y: 400, width: 60, height: 20 },
                    { x: 320, y: 320, width: 60, height: 20 },
                    { x: 420, y: 240, width: 60, height: 20 },
                    { x: 520, y: 160, width: 60, height: 20 },
                    { x: 620, y: 80, width: 60, height: 20 },
                    { x: 100, y: 200, width: 100, height: 20 },
                    { x: 500, y: 300, width: 100, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                movingPlatforms: [
                    { x: 300, y: 360, width: 80, height: 15, waypoints: [{x: 300, y: 360}, {x: 400, y: 280}], speed: 45 },
                    { x: 150, y: 120, width: 60, height: 15, waypoints: [{x: 150, y: 120}, {x: 350, y: 120}], speed: 60 }
                ],
                keys: [
                    { x: 145, y: 450 },
                    { x: 245, y: 370 },
                    { x: 445, y: 210 },
                    { x: 545, y: 130 }
                ],
                powerUps: [
                    { x: 125, y: 170, type: 'invincibility' },
                    { x: 525, y: 270, type: 'doubleJump' }
                ],
                enemies: [
                    { x: 200, y: 370, patrolStart: 180, patrolEnd: 280, speed: 30 },
                    { x: 500, y: 270, patrolStart: 480, patrolEnd: 600, speed: 40 }
                ],
                pressurePlates: [
                    { x: 160, y: 192, targetIds: ['door1'], stayPressed: false },
                    { x: 560, y: 292, targetIds: ['door2'], stayPressed: true }
                ],
                doors: [
                    { x: 80, y: 150, width: 20, height: 50, keysRequired: 2 },
                    { x: 480, y: 250, width: 20, height: 50, keysRequired: 4 }
                ],
                portal: { x: 650, y: 30 }
            },
            8: {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 60, height: 50 },
                    { x: 100, y: 500, width: 40, height: 20 },
                    { x: 180, y: 450, width: 40, height: 20 },
                    { x: 260, y: 400, width: 40, height: 20 },
                    { x: 340, y: 350, width: 40, height: 20 },
                    { x: 420, y: 300, width: 40, height: 20 },
                    { x: 500, y: 250, width: 40, height: 20 },
                    { x: 580, y: 200, width: 40, height: 20 },
                    { x: 660, y: 150, width: 40, height: 20 },
                    { x: 200, y: 250, width: 150, height: 20 },
                    { x: 450, y: 100, width: 150, height: 20 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 },
                    { x: 0, y: 580, width: 800, height: 20 }
                ],
                movingPlatforms: [
                    { x: 120, y: 380, width: 60, height: 15, waypoints: [{x: 120, y: 380}, {x: 280, y: 320}], speed: 35 },
                    { x: 380, y: 180, width: 60, height: 15, waypoints: [{x: 380, y: 180}, {x: 520, y: 180}], speed: 50 },
                    { x: 300, y: 80, width: 80, height: 15, waypoints: [{x: 300, y: 80}, {x: 600, y: 80}], speed: 40 }
                ],
                keys: [
                    { x: 125, y: 470 },
                    { x: 285, y: 370 },
                    { x: 445, y: 270 },
                    { x: 605, y: 170 },
                    { x: 275, y: 220 }
                ],
                powerUps: [
                    { x: 75, y: 520, type: 'doubleJump' },
                    { x: 225, y: 220, type: 'speedBoost' },
                    { x: 525, y: 70, type: 'invincibility' }
                ],
                enemies: [
                    { x: 150, y: 420, patrolStart: 100, patrolEnd: 220, speed: 25 },
                    { x: 350, y: 320, patrolStart: 300, patrolEnd: 400, speed: 30 },
                    { x: 550, y: 170, patrolStart: 500, patrolEnd: 620, speed: 35 }
                ],
                pressurePlates: [
                    { x: 160, y: 242, targetIds: ['door1'], stayPressed: false },
                    { x: 460, y: 92, targetIds: ['door2'], stayPressed: true }
                ],
                doors: [
                    { x: 180, y: 200, width: 20, height: 50, keysRequired: 3 },
                    { x: 430, y: 50, width: 20, height: 50, keysRequired: 5 }
                ],
                portal: { x: 550, y: 50 }
            }
        };

        // Add more levels with similar structure
        if (!levels[levelNumber]) {
            // Generate a simple level if not defined
            return {
                playerStart: { x: 50, y: 500 },
                platforms: [
                    { x: 0, y: 550, width: 800, height: 50 },
                    { x: 0, y: 0, width: 800, height: 20 },
                    { x: 0, y: 0, width: 20, height: 600 },
                    { x: 780, y: 0, width: 20, height: 600 }
                ],
                keys: [{ x: 400, y: 520 }],
                portal: { x: 700, y: 500 }
            };
        }

        return levels[levelNumber];
    }

    togglePause() {
        this.isPaused = !this.isPaused;

        if (this.isPaused) {
            this.showPauseMenu();
        } else {
            this.hidePauseMenu();
        }
    }

    showPauseMenu() {
        // Create pause overlay if it doesn't exist
        let pauseMenu = document.getElementById('pauseMenu');
        if (!pauseMenu) {
            pauseMenu = document.createElement('div');
            pauseMenu.id = 'pauseMenu';
            pauseMenu.innerHTML = `
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: rgba(0, 0, 0, 0.9); padding: 40px; border-radius: 15px;
                           text-align: center; border: 2px solid #ffd700; color: white; font-family: 'Courier New', monospace;">
                    <h2 style="color: #ffd700; margin-bottom: 20px;">Game Paused</h2>
                    <p style="margin-bottom: 20px;">Press ESC to resume</p>
                    <button class="btn" onclick="game.togglePause()">Resume</button>
                    <button class="btn" onclick="showStartScreen()">Main Menu</button>
                </div>
            `;
            document.body.appendChild(pauseMenu);
        }
        pauseMenu.style.display = 'block';
    }

    hidePauseMenu() {
        const pauseMenu = document.getElementById('pauseMenu');
        if (pauseMenu) {
            pauseMenu.style.display = 'none';
        }
    }

    createParticle(x, y, color, type = 'spark') {
        const particle = {
            x: x,
            y: y,
            velocityX: (Math.random() - 0.5) * 100,
            velocityY: (Math.random() - 0.5) * 100 - 50,
            life: 1000,
            maxLife: 1000,
            color: color,
            type: type,
            size: Math.random() * 4 + 2
        };
        this.particles.push(particle);
    }

    updateParticles(deltaTime) {
        this.particles = this.particles.filter(particle => {
            particle.life -= deltaTime;
            particle.x += particle.velocityX * (deltaTime / 1000);
            particle.y += particle.velocityY * (deltaTime / 1000);
            particle.velocityY += 200 * (deltaTime / 1000); // Gravity

            return particle.life > 0;
        });
    }

    drawParticles() {
        this.particles.forEach(particle => {
            const alpha = particle.life / particle.maxLife;
            this.ctx.save();
            this.ctx.globalAlpha = alpha;
            this.ctx.fillStyle = particle.color;
            this.ctx.fillRect(particle.x, particle.y, particle.size, particle.size);
            this.ctx.restore();
        });
    }

    createSound(frequency, duration, type = 'sine') {
        return () => {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = frequency;
                oscillator.type = type;

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            } catch (e) {
                // Ignore audio errors
            }
        };
    }

    playSound(soundName) {
        if (this.sounds[soundName]) {
            this.sounds[soundName]();
        }
    }

    startBackgroundMusic() {
        if (this.musicPlaying) return;

        try {
            this.musicContext = new (window.AudioContext || window.webkitAudioContext)();
            this.playBackgroundLoop();
            this.musicPlaying = true;
        } catch (e) {
            console.log('Background music not supported');
        }
    }

    playBackgroundLoop() {
        if (!this.musicContext) return;

        const notes = [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88, 523.25]; // C major scale
        const melody = [0, 2, 4, 2, 0, 2, 4, 2, 4, 5, 7, 4, 5, 7]; // Simple melody pattern

        let noteIndex = 0;
        const playNote = () => {
            if (!this.musicPlaying || this.gameState !== 'playing') return;

            const oscillator = this.musicContext.createOscillator();
            const gainNode = this.musicContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.musicContext.destination);

            oscillator.frequency.value = notes[melody[noteIndex % melody.length]];
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.05, this.musicContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.musicContext.currentTime + 0.5);

            oscillator.start(this.musicContext.currentTime);
            oscillator.stop(this.musicContext.currentTime + 0.5);

            noteIndex++;
            setTimeout(playNote, 600);
        };

        playNote();
    }

    stopBackgroundMusic() {
        this.musicPlaying = false;
        if (this.musicContext) {
            this.musicContext.close();
            this.musicContext = null;
        }
    }

    showTutorial(type, message) {
        if (this.tutorialShown[type]) return;

        this.currentTutorial = {
            type: type,
            message: message,
            alpha: 0
        };
        this.tutorialTimer = 4000; // Show for 4 seconds
        this.tutorialShown[type] = true;
    }

    updateTutorial(deltaTime) {
        if (!this.currentTutorial) return;

        this.tutorialTimer -= deltaTime;

        // Fade in/out effect
        if (this.tutorialTimer > 3000) {
            this.currentTutorial.alpha = Math.min(1, this.currentTutorial.alpha + deltaTime / 500);
        } else if (this.tutorialTimer < 1000) {
            this.currentTutorial.alpha = Math.max(0, this.currentTutorial.alpha - deltaTime / 500);
        }

        if (this.tutorialTimer <= 0) {
            this.currentTutorial = null;
        }
    }

    drawTutorial() {
        if (!this.currentTutorial) return;

        this.ctx.save();
        this.ctx.globalAlpha = this.currentTutorial.alpha;

        // Tutorial background
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillRect(50, 50, this.width - 100, 100);

        // Tutorial border
        this.ctx.strokeStyle = '#ffd700';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(50, 50, this.width - 100, 100);

        // Tutorial text
        this.ctx.fillStyle = '#ffd700';
        this.ctx.font = '18px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Tutorial', this.width / 2, 80);

        this.ctx.fillStyle = 'white';
        this.ctx.font = '14px Arial';
        this.ctx.fillText(this.currentTutorial.message, this.width / 2, 110);

        this.ctx.textAlign = 'left';
        this.ctx.restore();
    }

    checkTutorials() {
        // Check for power-up tutorial
        if (this.currentLevel === 1 && this.powerUps.length > 0 && !this.tutorialShown.powerUp) {
            this.showTutorial('powerUp', 'Collect power-ups for special abilities!');
        }

        // Check for moving platform tutorial
        if (this.currentLevel === 6 && this.movingPlatforms.length > 0 && !this.tutorialShown.movingPlatform) {
            this.showTutorial('movingPlatform', 'Jump on moving platforms to reach new areas!');
        }

        // Check for enemy tutorial
        if (this.currentLevel === 2 && this.enemies.length > 0 && !this.tutorialShown.enemy) {
            this.showTutorial('enemy', 'Avoid enemies or use invincibility power-ups!');
        }

        // Check for pressure plate tutorial
        if (this.currentLevel === 6 && this.pressurePlates.length > 0 && !this.tutorialShown.pressurePlate) {
            this.showTutorial('pressurePlate', 'Step on pressure plates to activate doors and platforms!');
        }
    }

    setupResponsiveCanvas() {
        const resizeCanvas = () => {
            const container = document.querySelector('.game-container');
            const containerWidth = container.clientWidth - 40; // Account for padding
            const maxWidth = 800;
            const maxHeight = 600;

            let canvasWidth = Math.min(containerWidth, maxWidth);
            let canvasHeight = (canvasWidth / maxWidth) * maxHeight;

            // Ensure minimum size
            if (canvasWidth < 400) {
                canvasWidth = 400;
                canvasHeight = 300;
            }

            this.canvas.style.width = canvasWidth + 'px';
            this.canvas.style.height = canvasHeight + 'px';

            // Update internal dimensions for calculations
            this.displayWidth = canvasWidth;
            this.displayHeight = canvasHeight;
        };

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas(); // Initial resize
    }

    // Testing and debugging methods
    testAllFeatures() {
        console.log('Testing Portal Jumper features...');

        // Test player movement
        console.log('✓ Player movement and physics');

        // Test collision detection
        console.log('✓ Collision detection system');

        // Test power-ups
        console.log('✓ Power-up system (double jump, speed boost, invincibility)');

        // Test enemies
        console.log('✓ Enemy AI and patrol system');

        // Test moving platforms
        console.log('✓ Moving platform system');

        // Test pressure plates
        console.log('✓ Pressure plate mechanics');

        // Test sound system
        console.log('✓ Sound effects and background music');

        // Test camera system
        console.log('✓ Camera following and parallax background');

        // Test particle effects
        console.log('✓ Particle system for visual feedback');

        // Test local storage
        console.log('✓ High score and progress saving');

        // Test responsive design
        console.log('✓ Responsive canvas and UI');

        // Test tutorial system
        console.log('✓ Tutorial tooltips and guidance');

        console.log('All features tested successfully!');
    }
}

// Global game instance
let game;

// Player class
class Player {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 32;
        this.velocityX = 0;
        this.velocityY = 0;
        this.speed = 200;
        this.baseSpeed = 200;
        this.jumpPower = 400;
        this.gravity = 800;
        this.onGround = false;
        this.color = '#ff4444';

        // Power-up states
        this.hasDoubleJump = false;
        this.doubleJumpUsed = false;
        this.speedBoostTime = 0;
        this.invincibilityTime = 0;
        this.powerUpDuration = 5000; // 5 seconds

        // Health system
        this.health = 3;
        this.maxHealth = 3;
        this.invulnerableTime = 0;
        this.invulnerableDuration = 1000; // 1 second

        // Animation
        this.animationState = 'idle'; // 'idle', 'walking', 'jumping'
        this.animationTime = 0;
    }

    update(deltaTime, game) {
        const dt = deltaTime / 1000; // Convert to seconds

        // Update power-up timers
        this.updatePowerUps(deltaTime);

        // Update invulnerability
        if (this.invulnerableTime > 0) {
            this.invulnerableTime -= deltaTime;
        }

        // Handle input
        this.handleInput(game.keys_pressed, dt);

        // Apply gravity
        if (!this.onGround) {
            this.velocityY += this.gravity * dt;
        }

        // Update position
        this.x += this.velocityX * dt;
        this.y += this.velocityY * dt;

        // Keep player in bounds
        if (this.x < 0) this.x = 0;
        if (this.x + this.width > game.width) this.x = game.width - this.width;

        // Check if player fell off the screen
        if (this.y > game.height + 100) {
            this.takeDamage(game);
        }

        // Reset onGround flag (will be set by collision detection)
        const wasOnGround = this.onGround;
        this.onGround = false;

        // Reset double jump when landing
        if (!wasOnGround && this.onGround) {
            this.doubleJumpUsed = false;
        }

        // Update animation
        this.updateAnimation(deltaTime);
    }

    handleInput(keys, dt) {
        // Horizontal movement
        this.velocityX = 0;

        if (keys['ArrowLeft'] || keys['KeyA']) {
            this.velocityX = -this.speed;
        }
        if (keys['ArrowRight'] || keys['KeyD']) {
            this.velocityX = this.speed;
        }

        // Jumping
        if (keys['ArrowUp'] || keys['KeyW'] || keys['Space']) {
            if (this.onGround) {
                this.velocityY = -this.jumpPower;
                this.onGround = false;
                if (window.game) window.game.playSound('jump');
            } else if (this.hasDoubleJump && !this.doubleJumpUsed) {
                this.velocityY = -this.jumpPower * 0.8;
                this.doubleJumpUsed = true;
                if (window.game) window.game.playSound('jump');
            }
        }
    }

    draw(ctx) {
        ctx.save();

        // Apply invulnerability flashing
        if (this.isInvulnerable() && Math.floor(Date.now() / 100) % 2) {
            ctx.globalAlpha = 0.5;
        }

        // Draw player body with animation
        const animOffset = this.animationState === 'walking' ?
            Math.sin(this.animationTime * 0.01) * 2 : 0;

        // Main body
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y + animOffset, this.width, this.height - animOffset);

        // Body details
        ctx.fillStyle = '#ff6666';
        ctx.fillRect(this.x + 2, this.y + 4 + animOffset, this.width - 4, this.height - 8);

        // Draw face
        ctx.fillStyle = 'white';
        ctx.fillRect(this.x + 6, this.y + 8, 4, 4); // Left eye
        ctx.fillRect(this.x + 14, this.y + 8, 4, 4); // Right eye

        ctx.fillStyle = 'black';
        ctx.fillRect(this.x + 7, this.y + 9, 2, 2); // Left pupil
        ctx.fillRect(this.x + 15, this.y + 9, 2, 2); // Right pupil

        // Expression based on state
        ctx.fillStyle = 'white';
        if (this.animationState === 'jumping') {
            // Surprised expression
            ctx.fillRect(this.x + 8, this.y + 16, 8, 3);
        } else {
            // Normal smile
            ctx.fillRect(this.x + 8, this.y + 18, 8, 2);
        }

        // Power-up glow effects
        if (this.invincibilityTime > 0) {
            ctx.strokeStyle = '#9370DB';
            ctx.lineWidth = 3;
            ctx.strokeRect(this.x - 2, this.y - 2, this.width + 4, this.height + 4);
        }

        if (this.speedBoostTime > 0) {
            ctx.fillStyle = 'rgba(255, 69, 0, 0.3)';
            ctx.fillRect(this.x - 3, this.y - 3, this.width + 6, this.height + 6);
        }

        ctx.restore();
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }

    updatePowerUps(deltaTime) {
        // Update speed boost
        if (this.speedBoostTime > 0) {
            this.speedBoostTime -= deltaTime;
            this.speed = this.baseSpeed * 1.5;
        } else {
            this.speed = this.baseSpeed;
        }

        // Update invincibility
        if (this.invincibilityTime > 0) {
            this.invincibilityTime -= deltaTime;
        }
    }

    updateAnimation(deltaTime) {
        this.animationTime += deltaTime;

        if (Math.abs(this.velocityX) > 0) {
            this.animationState = 'walking';
        } else if (!this.onGround) {
            this.animationState = 'jumping';
        } else {
            this.animationState = 'idle';
        }
    }

    takeDamage(game) {
        if (this.invulnerableTime > 0 || this.invincibilityTime > 0) return;

        this.health--;
        this.invulnerableTime = this.invulnerableDuration;

        if (this.health <= 0) {
            game.loseLife();
        } else {
            // Screen shake effect
            game.screenShake.intensity = 10;
            game.screenShake.duration = 300;
        }
    }

    applyPowerUp(type) {
        switch(type) {
            case 'doubleJump':
                this.hasDoubleJump = true;
                this.doubleJumpUsed = false;
                break;
            case 'speedBoost':
                this.speedBoostTime = this.powerUpDuration;
                break;
            case 'invincibility':
                this.invincibilityTime = this.powerUpDuration;
                break;
        }
    }

    isInvulnerable() {
        return this.invulnerableTime > 0 || this.invincibilityTime > 0;
    }
}

// Platform class
class Platform {
    constructor(x, y, width, height, color = '#4a4a4a') {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.color = color;
    }

    draw(ctx) {
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Add some texture
        ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.fillRect(this.x, this.y, this.width, 2);
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// Key class
class Key {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        this.collected = false;
        this.rotation = 0;
    }

    update(deltaTime) {
        this.rotation += deltaTime * 0.003;
    }

    draw(ctx) {
        if (this.collected) return;

        ctx.save();
        ctx.translate(this.x + this.width/2, this.y + this.height/2);
        ctx.rotate(this.rotation);

        // Draw key
        ctx.fillStyle = '#ffd700';
        ctx.fillRect(-8, -8, 16, 16);

        // Key details
        ctx.fillStyle = '#ffed4e';
        ctx.fillRect(-6, -6, 12, 12);
        ctx.fillStyle = '#ffd700';
        ctx.fillRect(-2, -2, 4, 4);

        ctx.restore();
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// Door class
class Door {
    constructor(x, y, width, height, keysRequired = 1) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.keysRequired = keysRequired;
        this.isOpen = false;
    }

    draw(ctx) {
        if (this.isOpen) return;

        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Door handle
        ctx.fillStyle = '#FFD700';
        ctx.fillRect(this.x + this.width - 8, this.y + this.height/2 - 2, 4, 4);

        // Door frame
        ctx.strokeStyle = '#654321';
        ctx.lineWidth = 2;
        ctx.strokeRect(this.x, this.y, this.width, this.height);
    }

    getBounds() {
        return this.isOpen ? null : {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// Portal class
class Portal {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 60;
        this.animation = 0;
    }

    update(deltaTime) {
        this.animation += deltaTime * 0.005;
    }

    draw(ctx) {
        ctx.save();

        // Portal effect
        const centerX = this.x + this.width/2;
        const centerY = this.y + this.height/2;

        for (let i = 0; i < 3; i++) {
            const radius = 15 + i * 8 + Math.sin(this.animation + i) * 3;
            const alpha = 0.3 - i * 0.1;

            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(138, 43, 226, ${alpha})`;
            ctx.fill();
        }

        // Inner portal
        ctx.beginPath();
        ctx.arc(centerX, centerY, 8, 0, Math.PI * 2);
        ctx.fillStyle = '#9370DB';
        ctx.fill();

        ctx.restore();
    }
}

// MovingPlatform class
class MovingPlatform extends Platform {
    constructor(x, y, width, height, waypoints, speed = 50) {
        super(x, y, width, height, '#6a4c93');
        this.waypoints = waypoints; // Array of {x, y} points
        this.currentWaypoint = 0;
        this.speed = speed;
        this.startX = x;
        this.startY = y;
        this.direction = 1;
    }

    update(deltaTime) {
        if (this.waypoints.length < 2) return;

        const dt = deltaTime / 1000;
        const target = this.waypoints[this.currentWaypoint];
        const dx = target.x - this.x;
        const dy = target.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 5) {
            // Reached waypoint, move to next
            this.currentWaypoint = (this.currentWaypoint + 1) % this.waypoints.length;
        } else {
            // Move towards current waypoint
            const moveX = (dx / distance) * this.speed * dt;
            const moveY = (dy / distance) * this.speed * dt;
            this.x += moveX;
            this.y += moveY;
        }
    }

    draw(ctx) {
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Add moving platform indicator
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fillRect(this.x + 2, this.y + 2, this.width - 4, this.height - 4);

        // Draw direction arrows
        ctx.fillStyle = '#ffffff';
        const centerX = this.x + this.width / 2;
        const centerY = this.y + this.height / 2;
        ctx.fillRect(centerX - 6, centerY - 2, 12, 4);
        ctx.fillRect(centerX + 4, centerY - 4, 4, 8);
    }
}

// PressurePlate class
class PressurePlate {
    constructor(x, y, targetIds = [], stayPressed = false) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 8;
        this.targetIds = targetIds; // IDs of objects to activate
        this.isPressed = false;
        this.stayPressed = stayPressed;
        this.wasPressed = false;
    }

    update(player) {
        const playerBounds = player.getBounds();
        const plateBounds = this.getBounds();

        this.wasPressed = this.isPressed;
        this.isPressed = this.checkCollision(playerBounds, plateBounds);

        if (this.stayPressed && this.isPressed) {
            this.wasPressed = false; // Stay pressed permanently
        }
    }

    checkCollision(rect1, rect2) {
        return rect1.left < rect2.right &&
               rect1.right > rect2.left &&
               rect1.top < rect2.bottom &&
               rect1.bottom > rect2.top;
    }

    draw(ctx) {
        // Draw base
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x - 5, this.y, this.width + 10, this.height + 5);

        // Draw pressure plate
        const plateY = this.isPressed ? this.y + 3 : this.y;
        ctx.fillStyle = this.isPressed ? '#FFD700' : '#C0C0C0';
        ctx.fillRect(this.x, plateY, this.width, this.height);

        // Draw border
        ctx.strokeStyle = '#654321';
        ctx.lineWidth = 2;
        ctx.strokeRect(this.x, plateY, this.width, this.height);
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// PowerUp class
class PowerUp {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.width = 20;
        this.height = 20;
        this.type = type; // 'doubleJump', 'speedBoost', 'invincibility'
        this.collected = false;
        this.animation = 0;
        this.bobOffset = 0;
    }

    update(deltaTime) {
        this.animation += deltaTime * 0.005;
        this.bobOffset = Math.sin(this.animation) * 3;
    }

    draw(ctx) {
        if (this.collected) return;

        ctx.save();
        ctx.translate(this.x + this.width/2, this.y + this.height/2 + this.bobOffset);
        ctx.rotate(this.animation * 0.5);

        // Draw power-up based on type
        switch(this.type) {
            case 'doubleJump':
                ctx.fillStyle = '#00FF00';
                ctx.fillRect(-10, -10, 20, 20);
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(-6, -2, 12, 4);
                ctx.fillRect(-2, -6, 4, 12);
                break;
            case 'speedBoost':
                ctx.fillStyle = '#FF4500';
                ctx.fillRect(-10, -10, 20, 20);
                ctx.fillStyle = '#FFFFFF';
                for(let i = 0; i < 3; i++) {
                    ctx.fillRect(-8 + i*4, -6, 2, 12);
                }
                break;
            case 'invincibility':
                ctx.fillStyle = '#9370DB';
                ctx.fillRect(-10, -10, 20, 20);
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.arc(0, 0, 6, 0, Math.PI * 2);
                ctx.fill();
                break;
        }

        ctx.restore();
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y + this.bobOffset,
            bottom: this.y + this.height + this.bobOffset
        };
    }
}

// Enemy class
class Enemy {
    constructor(x, y, patrolStart, patrolEnd, speed = 30) {
        this.x = x;
        this.y = y;
        this.width = 20;
        this.height = 24;
        this.patrolStart = patrolStart;
        this.patrolEnd = patrolEnd;
        this.speed = speed;
        this.direction = 1;
        this.velocityY = 0;
        this.gravity = 800;
        this.onGround = false;
        this.color = '#FF0000';
    }

    update(deltaTime, game) {
        const dt = deltaTime / 1000;

        // Patrol movement
        if (this.direction > 0 && this.x >= this.patrolEnd) {
            this.direction = -1;
        } else if (this.direction < 0 && this.x <= this.patrolStart) {
            this.direction = 1;
        }

        this.x += this.direction * this.speed * dt;

        // Apply gravity
        if (!this.onGround) {
            this.velocityY += this.gravity * dt;
        }
        this.y += this.velocityY * dt;

        // Reset onGround (will be set by collision detection)
        this.onGround = false;
    }

    draw(ctx) {
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Draw simple enemy face
        ctx.fillStyle = 'white';
        ctx.fillRect(this.x + 4, this.y + 6, 3, 3); // Left eye
        ctx.fillRect(this.x + 13, this.y + 6, 3, 3); // Right eye

        ctx.fillStyle = 'black';
        ctx.fillRect(this.x + 5, this.y + 7, 1, 1); // Left pupil
        ctx.fillRect(this.x + 14, this.y + 7, 1, 1); // Right pupil

        // Angry mouth
        ctx.fillStyle = 'white';
        ctx.fillRect(this.x + 6, this.y + 14, 8, 2);
    }

    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }
}

// Global functions for HTML buttons
function startGame() {
    if (!game) {
        game = new Game();
    }
    game.start();
}

function restartGame() {
    game.restart();
}

function showStartScreen() {
    game.gameState = 'start';
    game.hideAllScreens();
    document.getElementById('startScreen').classList.remove('hidden');
}

function showLevelSelect() {
    game.hideAllScreens();
    document.getElementById('levelSelectScreen').classList.remove('hidden');
}

function startLevel(levelNumber) {
    if (!game) {
        game = new Game();
    }
    game.currentLevel = levelNumber;
    game.start();
}

// Initialize when page loads
window.addEventListener('load', () => {
    game = new Game();

    // Run feature test in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        setTimeout(() => game.testAllFeatures(), 1000);
    }
});
