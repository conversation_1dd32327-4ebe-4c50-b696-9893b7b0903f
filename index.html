<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Jumper - 2D Platformer Puzzle Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: white;
        }

        .game-container {
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        h1 {
            margin-bottom: 20px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            color: #ffd700;
        }

        #gameCanvas {
            border: 3px solid #ffd700;
            border-radius: 10px;
            background: #87ceeb;
            display: block;
            margin: 0 auto;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .game-info {
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.2em;
            font-weight: bold;
        }

        .controls {
            margin-top: 15px;
            font-size: 0.9em;
            color: #cccccc;
        }

        .controls p {
            margin: 5px 0;
        }

        #startScreen, #gameOverScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #ffd700;
        }

        #startScreen h2, #gameOverScreen h2 {
            color: #ffd700;
            margin-bottom: 20px;
            font-size: 2em;
        }

        .btn {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            border: none;
            padding: 12px 24px;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: linear-gradient(45deg, #ffed4e, #ffd700);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>Portal Jumper</h1>
        
        <div id="startScreen">
            <h2>Welcome to Portal Jumper!</h2>
            <p>Navigate through platforms and solve puzzles to reach the portal!</p>
            <button class="btn" onclick="startGame()">Start Game</button>
        </div>

        <div id="gameOverScreen" class="hidden">
            <h2 id="gameOverTitle">Game Over!</h2>
            <p id="gameOverMessage">Try again!</p>
            <button class="btn" onclick="restartGame()">Restart</button>
            <button class="btn" onclick="showStartScreen()">Main Menu</button>
        </div>

        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="game-info">
            <div>Level: <span id="currentLevel">1</span></div>
            <div>Score: <span id="score">0</span></div>
            <div>Keys: <span id="keysCollected">0</span>/<span id="totalKeys">0</span></div>
        </div>
        
        <div class="controls">
            <p><strong>Controls:</strong></p>
            <p>Arrow Keys or WASD - Move</p>
            <p>Spacebar or Up Arrow - Jump</p>
            <p>Collect keys to unlock doors and reach the portal!</p>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
